"""
Translation agent with LangGraph workflow - supports multiple translation providers
"""
from typing import Op<PERSON>, AsyncGenerator, Union, Dict, Any, List
import json

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.translation_service import translation_service, TranslationRequest, TranslationProvider
from app.utils.logger import get_logger
from app.config import settings

logger = get_logger(__name__)


class TranslationAgent(BaseAgent):
    """Translation agent with multi-provider support"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="Translation Agent",
            description="Multi-provider translation agent with intelligent API selection"
        )

    def _build_graph(self):
        """Build LangGraph workflow for translation"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("detect_language", self._detect_language_node)
        workflow.add_node("select_provider", self._select_provider_node)
        workflow.add_node("translate_text", self._translate_text_node)
        workflow.add_node("validate_result", self._validate_result_node)
        workflow.add_node("fallback_translate", self._fallback_translate_node)
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        workflow.add_edge("validate_input", "detect_language")
        workflow.add_edge("detect_language", "select_provider")
        workflow.add_edge("select_provider", "translate_text")
        
        # Conditional routing after translation
        workflow.add_conditional_edges(
            "translate_text",
            self._route_after_translation,
            {
                "success": "finalize",
                "retry": "fallback_translate",
                "failed": "finalize"
            }
        )
        workflow.add_edge("fallback_translate", "finalize")
        workflow.add_edge("validate_result", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for translation agent")

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate translation input"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]
            
            # Validate required fields
            if "question" not in input_data or not input_data["question"]:
                raise ValueError("question field is required and cannot be empty")
            
            if not isinstance(input_data["question"], list):
                raise ValueError("question must be a list")
            
            for item in input_data["question"]:
                if not isinstance(item, dict) or "sourceText" not in item:
                    raise ValueError("Each question item must be a dict with 'sourceText' field")
            
            # Set defaults
            translate_options = input_data.get("translateOptions", {})
            # 确保有默认的 provider
            if "provider" not in translate_options:
                translate_options["provider"] = "llm_translate"

            state["step_results"]["validated_input"] = {
                "question": input_data["question"],
                "stream": input_data.get("stream", False),
                "translate_options": translate_options
            }
            
            logger.info(f"输入验证完成，文本数量: {len(input_data['question'])}")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state

    async def _detect_language_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Detect source language (simplified implementation)"""
        try:
            state["current_step"] = "detect_language"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            
            translate_options = validated_input["translate_options"]
            
            # Use provided language or default
            detected_lang = translate_options.get("src_lang", "en")
            target_lang = translate_options.get("tgt_lang", "zh")
            
            state["step_results"]["detected_lang"] = detected_lang
            state["step_results"]["target_lang"] = target_lang
            
            logger.info(f"语言检测完成: {detected_lang} -> {target_lang}")

        except Exception as e:
            state["error"] = f"Language detection failed: {str(e)}"
            logger.error(f"Language detection failed: {e}")

        return state

    async def _select_provider_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Select translation provider"""
        try:
            state["current_step"] = "select_provider"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]

            # Get provider from translate_options
            translate_options = validated_input.get("translate_options", {})
            provider = translate_options.get("provider", "doubao")

            # Validate provider
            if provider not in [TranslationProvider.DOUBAO, TranslationProvider.LLM_TRANSLATE]:
                logger.warning(f"Unknown provider: {provider}, using default doubao")
                provider = TranslationProvider.DOUBAO

            state["step_results"]["selected_provider"] = provider
            logger.info(f"选择翻译提供商: {provider}")

        except Exception as e:
            state["error"] = f"Provider selection failed: {str(e)}"
            logger.error(f"Provider selection failed: {e}")

        return state

    async def _translate_text_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Execute translation"""
        try:
            state["current_step"] = "translate_text"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]

            # Build translation request
            translate_options = {
                "src_lang": step_results["detected_lang"],
                "tgt_lang": step_results["target_lang"],
                "provider": step_results["selected_provider"]
            }
            translation_request = TranslationRequest(
                question=validated_input["question"],
                stream=validated_input["stream"],
                translate_options=translate_options
            )

            provider = step_results["selected_provider"]
            is_stream = translation_request.stream
            logger.info(f"开始翻译: provider={provider}, stream={is_stream}")

            # 根据提供商类型和流式参数选择处理方式

            if is_stream:
                # 流式模式：支持所有提供商的流式输出
                logger.info(f"provider-{provider}, stream")
                stream_generator = translation_service.stream_translate(translation_request)
                state["step_results"]["translation_stream"] = stream_generator
                # 流式模式下不设置translation_result，而是设置stream标志
                state["step_results"]["is_streaming"] = True

            else:
                # 非流式模式：按照现有逻辑处理（同步返回完整结果）
                logger.info(f"provider-{provider}, non-stream")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            state["step_results"]["attempt_count"] = state["step_results"].get("attempt_count", 0) + 1

            if state["step_results"].get("is_streaming"):
                logger.info("流式翻译已准备就绪")
            else:
                result = state["step_results"].get("translation_result")
                logger.info(f"翻译完成: code={result.code if result else 'unknown'}")

        except Exception as e:
            state["error"] = f"Translation failed: {str(e)}"
            logger.error(f"Translation failed: {e}")

        return state

    def _route_after_translation(self, state: AgentWorkflowState) -> str:
        """Route after translation based on result"""
        step_results = state["step_results"]

        # 检查是否为流式翻译
        if step_results.get("is_streaming"):
            logger.info("流式翻译模式，直接进入finalize")
            return "success"

        # 非流式翻译的原有逻辑
        result = step_results.get("translation_result")
        attempt_count = step_results.get("attempt_count", 0)

        if not result or result.code != "success":
            if attempt_count < 2:  # Max 1 retry
                logger.info("翻译失败，尝试回退策略")
                return "retry"
            else:
                logger.warning("翻译失败，已达到最大重试次数")
                return "failed"

        return "success"

    async def _validate_result_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate translation result"""
        try:
            state["current_step"] = "validate_result"
            # Additional result validation logic can be added here
            logger.info("翻译结果验证完成")

        except Exception as e:
            state["error"] = f"Result validation failed: {str(e)}"
            logger.error(f"Result validation failed: {e}")

        return state

    async def _fallback_translate_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Fallback translation strategy"""
        try:
            state["current_step"] = "fallback_translate"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            current_provider = step_results["selected_provider"]

            # Switch to alternative provider
            fallback_provider = (TranslationProvider.LLM_TRANSLATE
                                if current_provider == TranslationProvider.DOUBAO
                                else TranslationProvider.DOUBAO)

            logger.info(f"使用回退翻译策略: {current_provider} -> {fallback_provider}")

            # Build fallback translation request
            translate_options = {
                "src_lang": step_results["detected_lang"],
                "tgt_lang": step_results["target_lang"],
                "provider": fallback_provider
            }
            translation_request = TranslationRequest(
                question=validated_input["question"],
                stream=validated_input["stream"],
                translate_options=translate_options
            )

            # 根据回退提供商类型和流式参数选择处理方式
            provider = fallback_provider
            is_stream = translation_request.stream

            if provider == TranslationProvider.DOUBAO:
                # Doubao 提供商：非流式处理
                logger.info("回退到Doubao提供商，执行非流式翻译")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            elif provider == TranslationProvider.LLM_TRANSLATE and not is_stream:
                # LLM 提供商 + 非流式模式
                logger.info("回退到LLM提供商，执行非流式翻译")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            elif provider == TranslationProvider.LLM_TRANSLATE and is_stream:
                # LLM 提供商 + 流式模式
                logger.info("回退到LLM提供商，执行流式翻译")
                stream_generator = translation_service.stream_translate(translation_request)
                state["step_results"]["translation_stream"] = stream_generator
                state["step_results"]["is_streaming"] = True

            else:
                # 默认处理
                logger.warning(f"未知的回退提供商配置: {provider}, stream={is_stream}")
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result

            state["step_results"]["used_fallback"] = True

            if state["step_results"].get("is_streaming"):
                logger.info("回退流式翻译已准备就绪")
            else:
                result = state["step_results"].get("translation_result")
                logger.info(f"回退翻译完成: code={result.code if result else 'unknown'}")

        except Exception as e:
            state["error"] = f"Fallback translation failed: {str(e)}"
            logger.error(f"Fallback translation failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize translation result"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            # 检查是否为流式翻译
            if step_results.get("is_streaming"):
                # 流式翻译模式：将stream generator存储到final_output中
                stream_generator = step_results.get("translation_stream")
                if stream_generator:
                    state["final_output"] = stream_generator
                    logger.info("流式翻译流程完成，stream generator已准备就绪")
                else:
                    state["final_output"] = {
                        "code": "error",
                        "message": "Stream generator not found",
                        "data": []
                    }
                    logger.error("流式翻译失败：未找到stream generator")
            else:
                # 非流式翻译的原有逻辑
                result = step_results.get("translation_result")
                if result:
                    state["final_output"] = {
                        "code": result.code,
                        "message": result.message,
                        "data": result.data
                    }
                else:
                    state["final_output"] = {
                        "code": "error",
                        "message": "Translation failed",
                        "data": []
                    }

                logger.info("非流式翻译流程完成")

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}")

        return state

    # Required abstract method implementation
    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Process node logic (required by base class)"""
        return state

    # Direct access methods for API usage
    async def process_translation(self,
                                question: List[Dict[str, str]],
                                stream: bool = False,
                                translate_options: Optional[Dict[str, str]] = None,
                                provider: str = "doubao") -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """Translation processing with direct result passthrough"""

        # Prepare input data for LangGraph workflow
        translate_opts = translate_options or {}
        # 确保 provider 在 translate_options 中
        if "provider" not in translate_opts:
            translate_opts["provider"] = provider

        input_data = {
            "question": question,
            "stream": stream,
            "translateOptions": translate_opts
        }

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)

        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {
                "code": "error",
                "message": agent_state.error_message or "Translation failed",
                "data": []
            }

            if stream:
                # Stream error response
                async def error_stream():
                    yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                    yield "data: [DONE]\n\n"
                return error_stream()
            else:
                return error_response

        # Return successful result
        output_data = agent_state.output_data

        # 检查是否为流式翻译且有stream generator
        if stream and hasattr(output_data, '__aiter__'):
            # 流式翻译：直接返回stream generator
            logger.info("返回流式翻译生成器")
            return output_data
        elif stream:
            # 非流式结果的流式包装（用于Doubao等不支持流式的提供商）
            logger.info("返回非流式结果的流式包装")
            async def success_stream():
                yield f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
            return success_stream()
        else:
            # 非流式返回
            logger.info("返回非流式翻译结果")
            return output_data
